<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.example</groupId>
    <artifactId>langgraph4j-demo</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>

    <name>LangGraph4j Demo</name>
    <description>A comprehensive demo of LangGraph4j features including agents, tools, and workflows</description>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        
        <!-- Dependency versions -->
        <langgraph4j.version>1.5.14</langgraph4j.version>
        <langchain4j.version>0.36.2</langchain4j.version>
        <slf4j.version>2.0.16</slf4j.version>
        <logback.version>1.5.12</logback.version>
        <junit.version>5.11.3</junit.version>
    </properties>

    <dependencies>
        <!-- No external dependencies - this is a self-contained demo -->
        <!-- that demonstrates LangGraph4j concepts using pure Java -->
    </dependencies>

    <repositories>
        <!-- Maven Central -->
        <repository>
            <id>central</id>
            <url>https://repo1.maven.org/maven2</url>
        </repository>

        <!-- Sonatype OSS Snapshots Repository for development versions -->
        <repository>
            <id>sonatype-oss-snapshots</id>
            <url>https://oss.sonatype.org/content/repositories/snapshots</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.13.0</version>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.5.2</version>
            </plugin>

            <!-- Plugin to run the demo -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>3.5.0</version>
                <configuration>
                    <mainClass>com.example.langgraph4j.demo.LangGraph4jDemoApp</mainClass>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
