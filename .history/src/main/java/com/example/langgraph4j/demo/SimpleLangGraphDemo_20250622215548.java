package com.example.langgraph4j.demo;

import com.example.langgraph4j.demo.nodes.CounterNode;
import com.example.langgraph4j.demo.nodes.EchoNode;
import com.example.langgraph4j.demo.nodes.GreetingNode;
import com.example.langgraph4j.demo.state.DemoState;
import org.bsc.langgraph4j.StateGraph;
import org.bsc.langgraph4j.state.AgentState;

import java.util.Map;
import java.util.Scanner;
import java.util.concurrent.CompletableFuture;

import static org.bsc.langgraph4j.action.AsyncNodeAction.node_async;
import static org.bsc.langgraph4j.StateGraph.START;
import static org.bsc.langgraph4j.StateGraph.END;

/**
 * Simple LangGraph4j demonstration application.
 * 
 * This demo shows:
 * - Creating a state graph with multiple nodes
 * - Managing state between nodes
 * - Conditional routing between nodes
 * - Interactive conversation flow
 */
public class SimpleLangGraphDemo {
    
    private static final String GREETING_NODE = "greeting";
    private static final String COUNTER_NODE = "counter";
    private static final String ECHO_NODE = "echo";
    private static final String END = "__end__";
    
    public static void main(String[] args) {
        System.out.println("🚀 Starting LangGraph4j Simple Demo");
        System.out.println("=====================================");
        
        try {
            // Create and run the demo
            SimpleLangGraphDemo demo = new SimpleLangGraphDemo();
            demo.runDemo();
        } catch (Exception e) {
            System.err.println("❌ Error running demo: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public void runDemo() throws Exception {
        // Build the state graph
        StateGraph<DemoState> graph = buildGraph();
        
        // Compile the graph
        var compiledGraph = graph.compile();
        
        System.out.println("✅ Graph compiled successfully!");
        System.out.println("\n💬 Interactive Chat Demo");
        System.out.println("Commands: 'hello', 'count', 'goodbye', or any message");
        System.out.println("Type 'quit' to exit\n");
        
        // Initialize state
        DemoState initialState = new DemoState(Map.of(
            DemoState.USER_NAME_KEY, "Demo User",
            DemoState.COUNTER_KEY, 0
        ));
        
        Scanner scanner = new Scanner(System.in);
        DemoState currentState = initialState;
        
        while (true) {
            System.out.print("You: ");
            String userInput = scanner.nextLine().trim();
            
            if (userInput.equalsIgnoreCase("quit")) {
                System.out.println("👋 Thanks for trying the LangGraph4j demo!");
                break;
            }
            
            if (userInput.isEmpty()) {
                continue;
            }
            
            try {
                // Add user message to state
                currentState = new DemoState(Map.of(
                    DemoState.MESSAGES_KEY, currentState.messages(),
                    DemoState.COUNTER_KEY, currentState.counter(),
                    DemoState.USER_NAME_KEY, currentState.userName()
                ));
                
                // Add the user input as a message
                var stateWithMessage = currentState.mergeWith(Map.of(DemoState.MESSAGES_KEY, userInput));
                currentState = new DemoState(stateWithMessage);
                
                // Invoke the graph
                var result = compiledGraph.invoke(currentState);
                currentState = result;
                
                // Get the last message (bot response)
                var messages = currentState.messages();
                if (!messages.isEmpty()) {
                    String botResponse = messages.get(messages.size() - 1);
                    if (!botResponse.equals(userInput)) { // Don't echo the user's own message
                        System.out.println("Bot: " + botResponse);
                    }
                }
                
            } catch (Exception e) {
                System.err.println("❌ Error processing message: " + e.getMessage());
                e.printStackTrace();
            }
            
            System.out.println(); // Add spacing
        }
        
        scanner.close();
    }
    
    private StateGraph<DemoState> buildGraph() throws Exception {
        System.out.println("🔧 Building state graph...");
        
        // Create the state graph
        StateGraph<DemoState> graph = new StateGraph<>(DemoState.SCHEMA, DemoState::new);
        
        // Add nodes
        graph.addNode(GREETING_NODE, new GreetingNode());
        graph.addNode(COUNTER_NODE, new CounterNode());
        graph.addNode(ECHO_NODE, new EchoNode());
        
        // Set entry point
        graph.setEntryPoint(GREETING_NODE);
        
        // Add conditional edges
        graph.addConditionalEdges(
            GREETING_NODE,
            this::routeFromGreeting,
            Map.of(
                COUNTER_NODE, COUNTER_NODE,
                ECHO_NODE, ECHO_NODE,
                END, END
            )
        );
        
        graph.addConditionalEdges(
            COUNTER_NODE,
            this::routeFromCounter,
            Map.of(
                END, END
            )
        );
        
        graph.addConditionalEdges(
            ECHO_NODE,
            this::routeFromEcho,
            Map.of(
                END, END
            )
        );
        
        System.out.println("✅ Graph structure built");
        return graph;
    }
    
    // Routing logic
    private String routeFromGreeting(DemoState state) {
        if (state.messages().isEmpty()) {
            return ECHO_NODE;
        }
        
        String lastMessage = state.messages().get(state.messages().size() - 1);
        String lower = lastMessage.toLowerCase();
        
        // If greeting node handled it (added a response), we're done
        if (state.messages().size() >= 2) {
            String secondLast = state.messages().get(state.messages().size() - 2);
            if (!secondLast.equals(lastMessage)) {
                return END;
            }
        }
        
        // Route to appropriate node based on content
        if (lower.contains("count") || lower.contains("stats")) {
            return COUNTER_NODE;
        }
        
        return ECHO_NODE;
    }
    
    private String routeFromCounter(DemoState state) {
        return END;
    }
    
    private String routeFromEcho(DemoState state) {
        return END;
    }
}
