package com.example.langgraph4j.demo;

import com.example.langgraph4j.demo.nodes.CounterNode;
import com.example.langgraph4j.demo.nodes.EchoNode;
import com.example.langgraph4j.demo.nodes.GreetingNode;
import com.example.langgraph4j.demo.state.DemoState;
import org.bsc.langgraph4j.StateGraph;

import java.util.Map;
import java.util.Scanner;
import java.util.concurrent.CompletableFuture;

import static org.bsc.langgraph4j.action.AsyncNodeAction.node_async;
import static org.bsc.langgraph4j.StateGraph.START;
import static org.bsc.langgraph4j.StateGraph.END;

/**
 * Simple LangGraph4j demonstration application.
 * 
 * This demo shows:
 * - Creating a state graph with multiple nodes
 * - Managing state between nodes
 * - Conditional routing between nodes
 * - Interactive conversation flow
 */
public class SimpleLangGraphDemo {
    
    private static final String GREETING_NODE = "greeting";
    private static final String COUNTER_NODE = "counter";
    private static final String ECHO_NODE = "echo";
    private static final String END = "__end__";
    
    public static void main(String[] args) {
        System.out.println("🚀 Starting LangGraph4j Simple Demo");
        System.out.println("=====================================");
        
        try {
            // Create and run the demo
            SimpleLangGraphDemo demo = new SimpleLangGraphDemo();
            demo.runDemo();
        } catch (Exception e) {
            System.err.println("❌ Error running demo: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public void runDemo() throws Exception {
        // Build the state graph
        StateGraph<DemoState> graph = buildGraph();
        
        // Compile the graph
        var compiledGraph = graph.compile();
        
        System.out.println("✅ Graph compiled successfully!");
        System.out.println("\n💬 Interactive Chat Demo");
        System.out.println("Commands: 'hello', 'count', 'goodbye', or any message");
        System.out.println("Type 'quit' to exit\n");
        
        // Initialize state with empty messages
        DemoState initialState = new DemoState(Map.of());

        Scanner scanner = new Scanner(System.in);
        DemoState currentState = initialState;
        
        while (true) {
            System.out.print("You: ");
            String userInput = scanner.nextLine().trim();
            
            if (userInput.equalsIgnoreCase("quit")) {
                System.out.println("👋 Thanks for trying the LangGraph4j demo!");
                break;
            }
            
            if (userInput.isEmpty()) {
                continue;
            }
            
            try {
                // Invoke the graph with initial state as Map
                var result = compiledGraph.invoke(Map.of(DemoState.MESSAGES_KEY, userInput));
                if (result.isPresent()) {
                    currentState = result.get();

                    // Get the last message (bot response)
                    var messages = currentState.messages();
                    if (!messages.isEmpty()) {
                        String botResponse = messages.get(messages.size() - 1);
                        if (!botResponse.equals(userInput)) { // Don't echo the user's own message
                            System.out.println("Bot: " + botResponse);
                        }
                    }
                }
                
            } catch (Exception e) {
                System.err.println("❌ Error processing message: " + e.getMessage());
                e.printStackTrace();
            }
            
            System.out.println(); // Add spacing
        }
        
        scanner.close();
    }
    
    private StateGraph<DemoState> buildGraph() throws Exception {
        System.out.println("🔧 Building state graph...");
        
        // Create the state graph
        StateGraph<DemoState> graph = new StateGraph<>(DemoState.SCHEMA, DemoState::new);
        
        // Add nodes using node_async wrapper
        graph.addNode(GREETING_NODE, node_async(new GreetingNode()));
        graph.addNode(COUNTER_NODE, node_async(new CounterNode()));
        graph.addNode(ECHO_NODE, node_async(new EchoNode()));

        // Set entry point using addEdge from START
        graph.addEdge(START, GREETING_NODE);
        
        // Add conditional edges
        graph.addConditionalEdges(
            GREETING_NODE,
            this::routeFromGreeting,
            Map.of(
                COUNTER_NODE, COUNTER_NODE,
                ECHO_NODE, ECHO_NODE
            )
        );
        
        // Counter and Echo nodes will end the graph automatically
        
        System.out.println("✅ Graph structure built");
        return graph;
    }
    
    // Routing logic - must return CompletableFuture<String>
    private CompletableFuture<String> routeFromGreeting(DemoState state) {
        if (state.messages().isEmpty()) {
            return CompletableFuture.completedFuture(ECHO_NODE);
        }

        String lastMessage = state.messages().get(state.messages().size() - 1);
        String lower = lastMessage.toLowerCase();

        // Route to appropriate node based on content
        if (lower.contains("count") || lower.contains("stats")) {
            return CompletableFuture.completedFuture(COUNTER_NODE);
        }

        return CompletableFuture.completedFuture(ECHO_NODE);
    }


}
