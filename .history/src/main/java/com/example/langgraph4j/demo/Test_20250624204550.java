package com.example.langgraph4j.demo;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Stream;

public class Test {
    public static void main(String[] args) {

        List<Integer> originalList = Arrays.asList(1, 2, 3);

        // 追加一个元素
        Stream<Integer> newStream = Stream.concat(originalList.stream(), Stream.of(4));

        newStream.forEach(System.out::println);


    }
}