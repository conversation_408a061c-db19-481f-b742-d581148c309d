package com.example.langgraph4j.demo;

import reactor.core.publisher.Flux;

public class Test {
    public static void main(String[] args) {

        Flux<Integer> originalFlux = Flux.just(1, 2, 3);


        new Thread(() -> {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            originalFlux.subscribe(System.out::println);
        }).start();


        // 追加一个元素
        Flux<Integer> newFlux = originalFlux.concatWith(Flux.just(4));

        newFlux.subscribe(System.out::println);


    }
}