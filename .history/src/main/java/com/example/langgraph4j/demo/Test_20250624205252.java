package com.example.langgraph4j.demo;

import reactor.core.publisher.Flux;

public class Test {
    public static void main(String[] args) {

        Flux<Integer> originalFlux = Flux.just(1, 2, 3);


        new Thread(() -> {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            originalFlux.subscribe( x -> {

                    // if x is 3 then concatwith 4
                    if (x == 3) {
                        originalFlux.concatWith(Flux.just(4));
                    }

            } );


        }).start();


        originalFlux.subscribe(System.out::println);


    }
}