package com.example.langgraph4j.demo.nodes;

import com.example.langgraph4j.demo.state.DemoState;
import org.bsc.langgraph4j.action.NodeAction;

import java.util.Map;

/**
 * A node that handles counting operations and provides statistics.
 */
public class CounterNode implements NodeAction<DemoState> {
    
    @Override
    public Map<String, Object> apply(DemoState state) {
        System.out.println("🔢 CounterNode: Processing count request...");
        
        String lastMessage = state.messages().isEmpty() ? "" : 
            state.messages().get(state.messages().size() - 1);
        
        if (!isCountRequest(lastMessage)) {
            // Not a count request, pass through
            return Map.of();
        }
        
        // Increment the counter and provide statistics
        int newCount = state.counter() + 1;
        int totalMessages = state.messages().size();
        
        String response = String.format("📊 Message Statistics:\n" +
                "- Counter value: %d\n" +
                "- Total messages exchanged: %d\n" +
                "- User: %s\n" +
                "- This is interaction #%d", 
                newCount, totalMessages, state.userName(), newCount);
        
        System.out.println("🔢 CounterNode: Providing statistics");
        
        // Return both the response message and the updated counter
        return Map.of(
            DemoState.MESSAGES_KEY, response,
            DemoState.COUNTER_KEY, newCount
        );
    }
    
    private boolean isCountRequest(String message) {
        if (message == null) return false;
        String lower = message.toLowerCase().trim();
        return lower.contains("count") || lower.contains("stats") || 
               lower.contains("statistics") || lower.contains("number");
    }
}
