package com.example.langgraph4j.demo.nodes;

import com.example.langgraph4j.demo.state.ChatState;
import org.bsc.langgraph4j.action.NodeAction;

import java.util.Map;

/**
 * A node that handles greetings and introductions.
 */
public class GreetingNode implements NodeAction<ChatState> {
    
    @Override
    public Map<String, Object> apply(ChatState state) {
        System.out.println("👋 GreetingNode: Processing greeting...");
        
        String lastMessage = state.messages().isEmpty() ? "" : 
            state.messages().get(state.messages().size() - 1);
        
        String response;
        
        // Check if this is a greeting
        if (isGreeting(lastMessage)) {
            response = String.format("Hello %s! 👋 I'm your AI assistant powered by LangGraph4j. " +
                    "I can help you with calculations, system information, and general questions. " +
                    "What would you like to know?", state.userName());
        } else if (isGoodbye(lastMessage)) {
            response = String.format("Goodbye %s! 👋 It was nice chatting with you. Have a great day!", 
                    state.userName());
        } else {
            // Not a greeting, pass through
            return Map.of();
        }
        
        System.out.println("👋 GreetingNode: Generated response: " + response);
        return state.addMessage("Assistant: " + response);
    }
    
    private boolean isGreeting(String message) {
        if (message == null) return false;
        String lower = message.toLowerCase();
        return lower.contains("hello") || lower.contains("hi") || lower.contains("hey") ||
               lower.contains("good morning") || lower.contains("good afternoon") || 
               lower.contains("good evening") || lower.contains("greetings");
    }
    
    private boolean isGoodbye(String message) {
        if (message == null) return false;
        String lower = message.toLowerCase();
        return lower.contains("goodbye") || lower.contains("bye") || lower.contains("farewell") ||
               lower.contains("see you") || lower.contains("take care");
    }
}
