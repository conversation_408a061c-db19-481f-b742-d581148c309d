package com.example.langgraph4j.demo.state;

import com.example.langgraph4j.demo.framework.AgentState;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * State class for our chat demo.
 * This holds the conversation messages and any other state we need to track.
 */
public class ChatState extends AgentState {
    
    // State keys
    public static final String MESSAGES_KEY = "messages";
    public static final String USER_NAME_KEY = "user_name";
    public static final String CURRENT_AGENT_KEY = "current_agent";
    public static final String TOOL_CALLS_KEY = "tool_calls";
    public static final String CONVERSATION_CONTEXT_KEY = "context";
    
    // Initialize state with default values (simplified for demo)
    public static Map<String, Object> getDefaultState() {
        return Map.of(
            MESSAGES_KEY, new ArrayList<String>(),
            USER_NAME_KEY, "User",
            CURRENT_AGENT_KEY, "assistant",
            TOOL_CALLS_KEY, new ArrayList<String>(),
            CONVERSATION_CONTEXT_KEY, ""
        );
    }
    
    public ChatState(Map<String, Object> initData) {
        super(initData);
    }
    
    // Convenience methods to access state values
    
    @SuppressWarnings("unchecked")
    public List<String> messages() {
        return this.<List<String>>value(MESSAGES_KEY).orElse(List.of());
    }
    
    public String userName() {
        return this.<String>value(USER_NAME_KEY).orElse("User");
    }
    
    public String currentAgent() {
        return this.<String>value(CURRENT_AGENT_KEY).orElse("assistant");
    }
    
    @SuppressWarnings("unchecked")
    public List<String> toolCalls() {
        return this.<List<String>>value(TOOL_CALLS_KEY).orElse(List.of());
    }
    
    public String conversationContext() {
        return this.<String>value(CONVERSATION_CONTEXT_KEY).orElse("");
    }
    
    // Helper method to add a message
    public Map<String, Object> addMessage(String message) {
        return Map.of(MESSAGES_KEY, message);
    }
    
    // Helper method to set user name
    public Map<String, Object> setUserName(String name) {
        return Map.of(USER_NAME_KEY, name);
    }
    
    // Helper method to set current agent
    public Map<String, Object> setCurrentAgent(String agent) {
        return Map.of(CURRENT_AGENT_KEY, agent);
    }
    
    // Helper method to add tool call
    public Map<String, Object> addToolCall(String toolCall) {
        return Map.of(TOOL_CALLS_KEY, toolCall);
    }
    
    // Helper method to set context
    public Map<String, Object> setContext(String context) {
        return Map.of(CONVERSATION_CONTEXT_KEY, context);
    }
    
    @Override
    public String toString() {
        return String.format("ChatState{messages=%s, userName='%s', currentAgent='%s', toolCalls=%s, context='%s'}", 
                messages(), userName(), currentAgent(), toolCalls(), conversationContext());
    }
}
