package com.example.langgraph4j.demo.state;

import org.bsc.langgraph4j.state.AgentState;
import org.bsc.langgraph4j.state.Channel;
import org.bsc.langgraph4j.state.Channels;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * State class for our simple LangGraph4j demo.
 * This holds the conversation messages and other state we need to track.
 */
public class DemoState extends AgentState {
    
    // State keys
    public static final String MESSAGES_KEY = "messages";
    public static final String COUNTER_KEY = "counter";
    public static final String USER_NAME_KEY = "user_name";
    
    // Define the schema for the state
    public static final Map<String, Channel<?>> SCHEMA = Map.of(
            MESSAGES_KEY, Channels.appender(ArrayList::new),
            COUNTER_KEY, Channels.lastValue(0),
            USER_NAME_KEY, Channels.lastValue("User")
    );
    
    public DemoState(Map<String, Object> initData) {
        super(initData);
    }
    
    // Convenience methods to access state values
    
    @SuppressWarnings("unchecked")
    public List<String> messages() {
        return this.<List<String>>value(MESSAGES_KEY).orElse(List.of());
    }
    
    public Integer counter() {
        return this.<Integer>value(COUNTER_KEY).orElse(0);
    }
    
    public String userName() {
        return this.<String>value(USER_NAME_KEY).orElse("User");
    }
    
    // Helper method to add a message
    public Map<String, Object> addMessage(String message) {
        return Map.of(MESSAGES_KEY, message);
    }
    
    // Helper method to increment counter
    public Map<String, Object> incrementCounter() {
        return Map.of(COUNTER_KEY, counter() + 1);
    }
    
    // Helper method to set user name
    public Map<String, Object> setUserName(String name) {
        return Map.of(USER_NAME_KEY, name);
    }
}
