package com.example.langgraph4j.demo.state;

import org.bsc.langgraph4j.state.AgentState;
import org.bsc.langgraph4j.state.Channel;
import org.bsc.langgraph4j.state.Channels;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * State class for our simple LangGraph4j demo.
 * This holds the conversation messages and other state we need to track.
 */
public class DemoState extends AgentState {

    // State keys
    public static final String MESSAGES_KEY = "messages";

    // Define the schema for the state - simplified to just messages
    public static final Map<String, Channel<?>> SCHEMA = Map.of(
            MESSAGES_KEY, Channels.appender(ArrayList::new)
    );

    public DemoState(Map<String, Object> initData) {
        super(initData);
    }
    
    // Convenience methods to access state values

    @SuppressWarnings("unchecked")
    public List<String> messages() {
        return this.<List<String>>value(MESSAGES_KEY).orElse(List.of());
    }

    // Helper method to add a message
    public Map<String, Object> addMessage(String message) {
        return Map.of(MESSAGES_KEY, message);
    }
}
