package com.example.langgraph4j.demo.tools;

import dev.langchain4j.agent.tool.Tool;
import dev.langchain4j.agent.tool.P;

/**
 * A simple calculator tool that can perform basic arithmetic operations.
 */
public class CalculatorTool {
    
    @Tool("Add two numbers together")
    public double add(@P("first number") double a, @P("second number") double b) {
        double result = a + b;
        System.out.println("🧮 Calculator: " + a + " + " + b + " = " + result);
        return result;
    }
    
    @Tool("Subtract second number from first number")
    public double subtract(@P("first number") double a, @P("second number") double b) {
        double result = a - b;
        System.out.println("🧮 Calculator: " + a + " - " + b + " = " + result);
        return result;
    }
    
    @Tool("Multiply two numbers")
    public double multiply(@P("first number") double a, @P("second number") double b) {
        double result = a * b;
        System.out.println("🧮 Calculator: " + a + " × " + b + " = " + result);
        return result;
    }
    
    @Tool("Divide first number by second number")
    public double divide(@P("dividend") double a, @P("divisor") double b) {
        if (b == 0) {
            throw new IllegalArgumentException("Cannot divide by zero!");
        }
        double result = a / b;
        System.out.println("🧮 Calculator: " + a + " ÷ " + b + " = " + result);
        return result;
    }
    
    @Tool("Calculate the power of a number")
    public double power(@P("base number") double base, @P("exponent") double exponent) {
        double result = Math.pow(base, exponent);
        System.out.println("🧮 Calculator: " + base + "^" + exponent + " = " + result);
        return result;
    }
    
    @Tool("Calculate the square root of a number")
    public double sqrt(@P("number") double number) {
        if (number < 0) {
            throw new IllegalArgumentException("Cannot calculate square root of negative number!");
        }
        double result = Math.sqrt(number);
        System.out.println("🧮 Calculator: √" + number + " = " + result);
        return result;
    }
}
