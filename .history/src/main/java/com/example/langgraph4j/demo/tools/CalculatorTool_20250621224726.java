package com.example.langgraph4j.demo.tools;

/**
 * A simple calculator tool that can perform basic arithmetic operations.
 * In a real LangGraph4j application, methods would be annotated with @Tool.
 */
public class CalculatorTool {

    public double add(double a, double b) {
        double result = a + b;
        System.out.println("🧮 Calculator: " + a + " + " + b + " = " + result);
        return result;
    }
    
    public double subtract(double a, double b) {
        double result = a - b;
        System.out.println("🧮 Calculator: " + a + " - " + b + " = " + result);
        return result;
    }

    public double multiply(double a, double b) {
        double result = a * b;
        System.out.println("🧮 Calculator: " + a + " × " + b + " = " + result);
        return result;
    }

    public double divide(double a, double b) {
        if (b == 0) {
            throw new IllegalArgumentException("Cannot divide by zero!");
        }
        double result = a / b;
        System.out.println("🧮 Calculator: " + a + " ÷ " + b + " = " + result);
        return result;
    }

    public double power(double base, double exponent) {
        double result = Math.pow(base, exponent);
        System.out.println("🧮 Calculator: " + base + "^" + exponent + " = " + result);
        return result;
    }

    public double sqrt(double number) {
        if (number < 0) {
            throw new IllegalArgumentException("Cannot calculate square root of negative number!");
        }
        double result = Math.sqrt(number);
        System.out.println("🧮 Calculator: √" + number + " = " + result);
        return result;
    }
}
