package com.example.langgraph4j.demo.tools;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * A tool that provides system information.
 * In a real LangGraph4j application, methods would be annotated with @Tool.
 */
public class SystemInfoTool {

    public String getCurrentDateTime() {
        LocalDateTime now = LocalDateTime.now();
        String formatted = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        System.out.println("🕒 System: Current time is " + formatted);
        return "Current date and time: " + formatted;
    }
    
    public int getThreadCount() {
        int count = Thread.getAllStackTraces().size();
        System.out.println("🧵 System: Active threads count is " + count);
        return count;
    }

    public String getMemoryInfo() {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();
        
        String info = String.format(
            "Memory Info - Total: %d MB, Used: %d MB, Free: %d MB, Max: %d MB",
            totalMemory / (1024 * 1024),
            usedMemory / (1024 * 1024),
            freeMemory / (1024 * 1024),
            maxMemory / (1024 * 1024)
        );
        
        System.out.println("💾 System: " + info);
        return info;
    }
    
    @Tool("Get Java version information")
    public String getJavaVersion() {
        String version = System.getProperty("java.version");
        String vendor = System.getProperty("java.vendor");
        String info = "Java Version: " + version + " by " + vendor;
        System.out.println("☕ System: " + info);
        return info;
    }
    
    @Tool("Get operating system information")
    public String getOSInfo() {
        String osName = System.getProperty("os.name");
        String osVersion = System.getProperty("os.version");
        String osArch = System.getProperty("os.arch");
        String info = "OS: " + osName + " " + osVersion + " (" + osArch + ")";
        System.out.println("💻 System: " + info);
        return info;
    }
}
