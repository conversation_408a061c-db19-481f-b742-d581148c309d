package com.example.langgraph4j.demo;

import com.example.langgraph4j.demo.state.DemoState;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for the SimpleLangGraphDemo.
 */
public class SimpleLangGraphDemoTest {
    
    private DemoState testState;
    
    @BeforeEach
    void setUp() {
        testState = new DemoState(Map.of());
    }

    @Test
    void testStateCreation() {
        assertNotNull(testState);
        assertTrue(testState.messages().isEmpty());
    }
    
    @Test
    void testAddMessage() {
        var update = testState.addMessage("Hello World");
        assertNotNull(update);
        assertTrue(update.containsKey(DemoState.MESSAGES_KEY));
        assertEquals("Hello World", update.get(DemoState.MESSAGES_KEY));
    }
    
    @Test
    void testIncrementCounter() {
        var update = testState.incrementCounter();
        assertNotNull(update);
        assertTrue(update.containsKey(DemoState.COUNTER_KEY));
        assertEquals(1, update.get(DemoState.COUNTER_KEY));
    }
    
    @Test
    void testSetUserName() {
        var update = testState.setUserName("NewUser");
        assertNotNull(update);
        assertTrue(update.containsKey(DemoState.USER_NAME_KEY));
        assertEquals("NewUser", update.get(DemoState.USER_NAME_KEY));
    }
}
