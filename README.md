# LangGraph4j Simple Demo

A simple demonstration of LangGraph4j features including state management, node routing, and interactive conversation flow.

## What This Demo Shows

This demo illustrates key LangGraph4j concepts:

- **State Management**: Using `AgentState` to maintain conversation state
- **Node Actions**: Creating custom nodes that process state and return updates
- **Conditional Routing**: Routing between nodes based on state content
- **Graph Compilation**: Building and compiling a state graph for execution
- **Interactive Flow**: Running an interactive chat session

## Project Structure

```
src/main/java/com/example/langgraph4j/demo/
├── SimpleLangGraphDemo.java     # Main application class
├── state/
│   └── DemoState.java          # State management class
└── nodes/
    ├── GreetingNode.java       # Handles greetings and goodbyes
    ├── CounterNode.java        # Provides message statistics
    └── EchoNode.java           # Fallback echo functionality
```

## Features

### Nodes

1. **GreetingNode**: Responds to greetings ("hello", "hi") and goodbyes ("bye", "goodbye")
2. **CounterNode**: Provides statistics when asked ("count", "stats")
3. **EchoNode**: Fallback node that echoes back unrecognized messages

### State

The `DemoState` class manages:
- **Messages**: List of conversation messages
- **Counter**: Interaction counter
- **User Name**: Current user identifier

## How to Run

### Prerequisites

- Java 17 or higher
- Maven 3.6 or higher

### Build and Run

1. **Compile the project**:
   ```bash
   mvn clean compile
   ```

2. **Run tests**:
   ```bash
   mvn test
   ```

3. **Run the demo**:
   ```bash
   mvn exec:java
   ```

### Interactive Commands

Once running, try these commands:
- `hello` - Get a greeting
- `count` - See message statistics
- `goodbye` - Say farewell
- Any other text - Get an echo response
- `quit` - Exit the demo

## Example Session

```
🚀 Starting LangGraph4j Simple Demo
=====================================
🔧 Building state graph...
✅ Graph structure built
✅ Graph compiled successfully!

💬 Interactive Chat Demo
Commands: 'hello', 'count', 'goodbye', or any message
Type 'quit' to exit

You: hello
👋 GreetingNode: Processing greeting...
👋 GreetingNode: Responding with greeting
Bot: Hello Demo User! 👋 Welcome to the LangGraph4j demo! I'm a simple chatbot that can count messages and respond to greetings. Try saying 'count' to see how many messages we've exchanged!

You: count
🔢 CounterNode: Processing count request...
🔢 CounterNode: Providing statistics
Bot: 📊 Message Statistics:
- Counter value: 1
- Total messages exchanged: 3
- User: Demo User
- This is interaction #1

You: test message
🔄 EchoNode: Processing general message...
🔄 EchoNode: Echoing message back
Bot: 🤖 Echo: You said 'test message'. I'm a simple demo bot. Try saying 'hello', 'count', or 'goodbye'!

You: quit
👋 Thanks for trying the LangGraph4j demo!
```

## Key Concepts Demonstrated

### 1. State Schema Definition
```java
public static final Map<String, Channel<?>> SCHEMA = Map.of(
    MESSAGES_KEY, Channels.appender(ArrayList::new),
    COUNTER_KEY, Channels.lastValue(0),
    USER_NAME_KEY, Channels.lastValue("User")
);
```

### 2. Node Implementation
```java
public class GreetingNode implements NodeAction<DemoState> {
    @Override
    public Map<String, Object> apply(DemoState state) {
        // Process state and return updates
        return Map.of(DemoState.MESSAGES_KEY, response);
    }
}
```

### 3. Conditional Routing
```java
graph.addConditionalEdges(
    GREETING_NODE,
    this::routeFromGreeting,
    Map.of(
        COUNTER_NODE, COUNTER_NODE,
        ECHO_NODE, ECHO_NODE,
        END, END
    )
);
```

## Next Steps

To extend this demo, you could:
- Add more sophisticated NLP processing
- Integrate with external APIs
- Add persistent state storage
- Implement more complex routing logic
- Add tool calling capabilities

## Dependencies

- **LangGraph4j Core**: 1.5.14
- **SLF4J**: 2.0.16
- **Logback**: 1.5.12
- **JUnit**: 5.11.3 (for testing)
