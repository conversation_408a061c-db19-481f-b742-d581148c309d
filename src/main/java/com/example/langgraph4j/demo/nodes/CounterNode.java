package com.example.langgraph4j.demo.nodes;

import com.example.langgraph4j.demo.state.DemoState;
import org.bsc.langgraph4j.action.NodeAction;

import java.util.Map;

/**
 * A node that handles counting operations and provides statistics.
 */
public class CounterNode implements NodeAction<DemoState> {
    
    @Override
    public Map<String, Object> apply(DemoState state) {
        System.out.println("🔢 CounterNode: Processing count request...");
        
        String lastMessage = state.messages().isEmpty() ? "" : 
            state.messages().get(state.messages().size() - 1);
        
        if (!isCountRequest(lastMessage)) {
            // Not a count request, pass through
            return Map.of();
        }

        // Provide simple statistics
        int totalMessages = state.messages().size();

        String response = String.format("📊 Message Statistics:\n" +
                "- Total messages exchanged: %d\n" +
                "- This conversation has %d messages so far",
                totalMessages, totalMessages);

        System.out.println("🔢 CounterNode: Providing statistics");

        // Return the response message
        return Map.of(DemoState.MESSAGES_KEY, response);
    }
    
    private boolean isCountRequest(String message) {
        if (message == null) return false;
        String lower = message.toLowerCase().trim();
        return lower.contains("count") || lower.contains("stats") || 
               lower.contains("statistics") || lower.contains("number");
    }
}
