package com.example.langgraph4j.demo.nodes;

import com.example.langgraph4j.demo.state.DemoState;
import org.bsc.langgraph4j.action.NodeAction;

import java.util.Map;

/**
 * A fallback node that echoes back user messages with some processing.
 */
public class EchoNode implements NodeAction<DemoState> {
    
    @Override
    public Map<String, Object> apply(DemoState state) {
        System.out.println("🔄 EchoNode: Processing general message...");
        
        String lastMessage = state.messages().isEmpty() ? "No message" : 
            state.messages().get(state.messages().size() - 1);
        
        String response = String.format("🤖 Echo: You said '%s'. " +
                "I'm a simple demo bot. Try saying 'hello', 'count', or 'goodbye'!", 
                lastMessage);
        
        System.out.println("🔄 EchoNode: Echoing message back");
        return Map.of(DemoState.MESSAGES_KEY, response);
    }
}
