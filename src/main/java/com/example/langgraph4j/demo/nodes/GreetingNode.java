package com.example.langgraph4j.demo.nodes;

import com.example.langgraph4j.demo.state.DemoState;
import org.bsc.langgraph4j.action.NodeAction;

import java.util.Map;

/**
 * A node that handles greetings and introductions.
 */
public class GreetingNode implements NodeAction<DemoState> {
    
    @Override
    public Map<String, Object> apply(DemoState state) {
        System.out.println("👋 GreetingNode: Processing greeting...");
        
        String lastMessage = state.messages().isEmpty() ? "" : 
            state.messages().get(state.messages().size() - 1);
        
        String response;
        
        // Check if this is a greeting
        if (isGreeting(lastMessage)) {
            response = "Hello! 👋 Welcome to the LangGraph4j demo! " +
                    "I'm a simple chatbot that can respond to greetings. " +
                    "Try saying different things to see how I respond!";
        } else if (isGoodbye(lastMessage)) {
            response = "Goodbye! 👋 Thanks for trying the LangGraph4j demo!";
        } else {
            // Not a greeting, pass through
            return Map.of();
        }
        
        System.out.println("👋 GreetingNode: Responding with greeting");
        return Map.of(DemoState.MESSAGES_KEY, response);
    }
    
    private boolean isGreeting(String message) {
        if (message == null) return false;
        String lower = message.toLowerCase().trim();
        return lower.contains("hello") || lower.contains("hi") || 
               lower.contains("hey") || lower.contains("greetings") ||
               lower.equals("start");
    }
    
    private boolean isGoodbye(String message) {
        if (message == null) return false;
        String lower = message.toLowerCase().trim();
        return lower.contains("bye") || lower.contains("goodbye") || 
               lower.contains("farewell") || lower.contains("see you");
    }
}
