package com.example.langgraph4j.demo;

import com.example.langgraph4j.demo.state.DemoState;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for the SimpleLangGraphDemo.
 */
public class SimpleLangGraphDemoTest {
    
    private DemoState testState;
    
    @BeforeEach
    void setUp() {
        testState = new DemoState(Map.of());
    }

    @Test
    void testStateCreation() {
        assertNotNull(testState);
        assertTrue(testState.messages().isEmpty());
    }
    
    @Test
    void testAddMessage() {
        var update = testState.addMessage("Hello World");
        assertNotNull(update);
        assertTrue(update.containsKey(DemoState.MESSAGES_KEY));
        assertEquals("Hello World", update.get(DemoState.MESSAGES_KEY));
    }
}
